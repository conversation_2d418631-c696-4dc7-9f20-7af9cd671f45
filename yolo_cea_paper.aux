\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\citation{2022Inspection}
\citation{2024Region}
\citation{2023Summary,2022An}
\citation{2021Real}
\citation{9400959}
\citation{2021InsuDet,2022Improved}
\citation{2021Improved,coatings13050880}
\citation{han2022insulator,DBLP:journals/eaai/FengYYYLSZ25}
\citation{2024CACS-YOLO}
\@writefile{toc}{\contentsline {section}{\numberline {I}Introduction}{1}{section.1}\protected@file@percent }
\citation{yolo11}
\@writefile{toc}{\contentsline {section}{\numberline {II}Method}{2}{section.2}\protected@file@percent }
\newlabel{sec:method}{{II}{2}{Method}{section.2}{}}
\citation{2021An}
\@writefile{lof}{\contentsline {figure}{\numberline {1}{\ignorespaces Overall architecture of the YOLO-CEA.}}{3}{figure.1}\protected@file@percent }
\newlabel{fig_architecture}{{1}{3}{Overall architecture of the YOLO-CEA}{figure.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-A}}C3k2-HTC Module}{3}{subsection.2.1}\protected@file@percent }
\citation{shi2024transnext}
\citation{li2024rethinking}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-B}}Aligned-Balanced Feature Pyramid Network (AB-FPN)}{4}{subsection.2.2}\protected@file@percent }
\newlabel{eq:sni}{{5}{4}{Aligned-Balanced Feature Pyramid Network (AB-FPN)}{equation.2.5}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {2}{\ignorespaces Architecture of the C3k2-HTC module showing the hybrid design that combines Transformer and CNN components. The HTCBlock processes features through parallel Transformer and Bottleneck branches, enabling both global context modeling and local feature extraction.}}{5}{figure.2}\protected@file@percent }
\newlabel{fig_htc}{{2}{5}{Architecture of the C3k2-HTC module showing the hybrid design that combines Transformer and CNN components. The HTCBlock processes features through parallel Transformer and Bottleneck branches, enabling both global context modeling and local feature extraction}{figure.2}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {3}{\ignorespaces Illustration of the Soft Nearest Neighbor Interpolation (SNI) module. The module applies energy normalization with coefficient $\alpha = 1/k^2$ to prevent feature misalignment during upsampling in the top-down path of AB-FPN.}}{5}{figure.3}\protected@file@percent }
\newlabel{fig_sni}{{3}{5}{Illustration of the Soft Nearest Neighbor Interpolation (SNI) module. The module applies energy normalization with coefficient $\alpha = 1/k^2$ to prevent feature misalignment during upsampling in the top-down path of AB-FPN}{figure.3}{}}
\citation{chollet2017xception}
\citation{hendrycks2016gaussian}
\citation{zhang2018shufflenet}
\citation{tian2019fcos}
\citation{li2021generalized}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-C}}Shared Location Quality Modulation Detection Head (SLQMD)}{6}{subsection.2.3}\protected@file@percent }
\citation{tao2018detection}
\citation{vkdw-x769-21}
\@writefile{lof}{\contentsline {figure}{\numberline {4}{\ignorespaces Structure of the GSConv Enhancement (GSE) module used in the bottom-up path of AB-FPN. The module employs a hybrid design with parallel dense and sparse convolution branches followed by channel shuffle for efficient downsampling.}}{7}{figure.4}\protected@file@percent }
\newlabel{fig_gsconve}{{4}{7}{Structure of the GSConv Enhancement (GSE) module used in the bottom-up path of AB-FPN. The module employs a hybrid design with parallel dense and sparse convolution branches followed by channel shuffle for efficient downsampling}{figure.4}{}}
\@writefile{toc}{\contentsline {section}{\numberline {III}Experimental}{7}{section.3}\protected@file@percent }
\newlabel{sec:experimental}{{III}{7}{Experimental}{section.3}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-A}}Dataset}{7}{subsection.3.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-B}}Experimental Setup}{7}{subsection.3.2}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {5}{\ignorespaces Architecture of the Shared Location Quality Modulation Detection Head (SLQMD). The design features cross-scale parameter sharing and location quality modulation mechanism to enhance both parameter efficiency and prediction reliability.}}{8}{figure.5}\protected@file@percent }
\newlabel{fig_slqmd}{{5}{8}{Architecture of the Shared Location Quality Modulation Detection Head (SLQMD). The design features cross-scale parameter sharing and location quality modulation mechanism to enhance both parameter efficiency and prediction reliability}{figure.5}{}}
\@writefile{lot}{\contentsline {table}{\numberline {I}{\ignorespaces Hardware and Software Configuration for the Experiments}}{8}{table.1}\protected@file@percent }
\newlabel{tab:hardware}{{I}{8}{Hardware and Software Configuration for the Experiments}{table.1}{}}
\@writefile{lot}{\contentsline {table}{\numberline {II}{\ignorespaces Experimental Parameters}}{8}{table.2}\protected@file@percent }
\newlabel{tab:params}{{II}{8}{Experimental Parameters}{table.2}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-C}}Evaluation Metrics}{8}{subsection.3.3}\protected@file@percent }
\citation{liu2024yolo}
\citation{jin2025real}
\citation{10471592}
\citation{tan2020efficientdet}
\citation{wu2025crl}
\citation{jin2025real}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-D}}Experimental Results Analysis}{9}{subsection.3.4}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-D}1}Experimental Validation and Analysis of the C3k2 Module}{9}{subsubsection.3.4.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-D}2}Experimental Validation and Analysis of FPN}{9}{subsubsection.3.4.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-D}3}Experimental Validation and Analysis of the Head}{9}{subsubsection.3.4.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-D}4}Ablation Study}{9}{subsubsection.3.4.4}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {III}{\ignorespaces Performance Comparison of C3k2 Module Variants}}{10}{table.3}\protected@file@percent }
\newlabel{tab:c3k2_results}{{III}{10}{Performance Comparison of C3k2 Module Variants}{table.3}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {6}{\ignorespaces Visualization comparison of attention heatmaps generated by different C3k2 module variants. The figure shows how C3k2-HTC achieves more focused attention on insulator features while suppressing background noise compared to baseline methods.}}{10}{figure.6}\protected@file@percent }
\newlabel{fig_hotmap}{{6}{10}{Visualization comparison of attention heatmaps generated by different C3k2 module variants. The figure shows how C3k2-HTC achieves more focused attention on insulator features while suppressing background noise compared to baseline methods}{figure.6}{}}
\@writefile{lot}{\contentsline {table}{\numberline {IV}{\ignorespaces Performance Comparison of FPN Variants}}{10}{table.4}\protected@file@percent }
\newlabel{tab:fpn_results}{{IV}{10}{Performance Comparison of FPN Variants}{table.4}{}}
\citation{ren2016faster}
\citation{cai2018cascade}
\citation{zhang2020dynamic}
\citation{pang2019libra}
\citation{lin2017focal}
\citation{yolov3}
\citation{yolov5}
\citation{yolov6}
\citation{yolov7}
\citation{yolov8}
\citation{yolov9}
\citation{yolov10}
\citation{yolo11}
\citation{yolov12}
\citation{wang2024mci}
\citation{yolov3}
\citation{yolov5}
\citation{yolov6}
\citation{yolov7}
\citation{yolov8}
\citation{yolov9}
\citation{yolov10}
\citation{yolo11}
\citation{yolov12}
\citation{tao2024snakenet}
\citation{pradeep2025improved}
\@writefile{lot}{\contentsline {table}{\numberline {V}{\ignorespaces Performance Comparison of Detection Head Variants}}{11}{table.5}\protected@file@percent }
\newlabel{tab:head_results}{{V}{11}{Performance Comparison of Detection Head Variants}{table.5}{}}
\@writefile{lot}{\contentsline {table}{\numberline {VI}{\ignorespaces Ablation Study Results}}{11}{table.6}\protected@file@percent }
\newlabel{tab:ablation}{{VI}{11}{Ablation Study Results}{table.6}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-D}5}Comparative Experiments}{11}{subsubsection.3.4.5}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {7}{\ignorespaces Qualitative comparison of detection results between baseline YOLOv11n and proposed YOLO-CEA across diverse challenging scenarios. The comparison demonstrates YOLO-CEA's superior confidence scores and reduced missed detections in complex backgrounds.}}{12}{figure.7}\protected@file@percent }
\newlabel{fig_compared}{{7}{12}{Qualitative comparison of detection results between baseline YOLOv11n and proposed YOLO-CEA across diverse challenging scenarios. The comparison demonstrates YOLO-CEA's superior confidence scores and reduced missed detections in complex backgrounds}{figure.7}{}}
\@writefile{lot}{\contentsline {table}{\numberline {VII}{\ignorespaces Performance Comparison with State-of-the-Art Methods}}{13}{table.7}\protected@file@percent }
\newlabel{tab:comparison}{{VII}{13}{Performance Comparison with State-of-the-Art Methods}{table.7}{}}
\@writefile{lot}{\contentsline {table}{\numberline {VIII}{\ignorespaces Performance Comparison on CPLID Dataset}}{13}{table.8}\protected@file@percent }
\newlabel{tab:cplid_results}{{VIII}{13}{Performance Comparison on CPLID Dataset}{table.8}{}}
\@writefile{toc}{\contentsline {section}{\numberline {IV}Conclusion}{13}{section.4}\protected@file@percent }
\newlabel{sec:conclusion}{{IV}{13}{Conclusion}{section.4}{}}
\bibstyle{IEEEtran}
\bibdata{reference}
\bibcite{2022Inspection}{1}
\bibcite{2024Region}{2}
\bibcite{2023Summary}{3}
\bibcite{2022An}{4}
\bibcite{2021Real}{5}
\bibcite{9400959}{6}
\bibcite{2021InsuDet}{7}
\bibcite{2022Improved}{8}
\bibcite{2021Improved}{9}
\bibcite{coatings13050880}{10}
\bibcite{han2022insulator}{11}
\bibcite{DBLP:journals/eaai/FengYYYLSZ25}{12}
\bibcite{2024CACS-YOLO}{13}
\bibcite{yolo11}{14}
\bibcite{2021An}{15}
\bibcite{shi2024transnext}{16}
\bibcite{li2024rethinking}{17}
\bibcite{chollet2017xception}{18}
\bibcite{hendrycks2016gaussian}{19}
\bibcite{zhang2018shufflenet}{20}
\bibcite{tian2019fcos}{21}
\bibcite{li2021generalized}{22}
\bibcite{tao2018detection}{23}
\bibcite{vkdw-x769-21}{24}
\bibcite{liu2024yolo}{25}
\bibcite{jin2025real}{26}
\bibcite{10471592}{27}
\bibcite{tan2020efficientdet}{28}
\bibcite{wu2025crl}{29}
\bibcite{ren2016faster}{30}
\bibcite{cai2018cascade}{31}
\bibcite{zhang2020dynamic}{32}
\bibcite{pang2019libra}{33}
\bibcite{lin2017focal}{34}
\bibcite{yolov3}{35}
\@writefile{toc}{\contentsline {section}{References}{14}{section*.2}\protected@file@percent }
\bibcite{yolov5}{36}
\bibcite{yolov6}{37}
\bibcite{yolov7}{38}
\bibcite{yolov8}{39}
\bibcite{yolov9}{40}
\bibcite{yolov10}{41}
\bibcite{yolov12}{42}
\bibcite{wang2024mci}{43}
\bibcite{tao2024snakenet}{44}
\bibcite{pradeep2025improved}{45}
\gdef \@abspage@last{15}
