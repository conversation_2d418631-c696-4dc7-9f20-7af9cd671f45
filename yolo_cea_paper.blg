This is BibTeX, Version 0.99d (TeX Live 2024)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: yolo_cea_paper.aux
The style file: IEEEtran.bst
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated wiz_functions (elt_size=4) to 6000 items from 3000.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Database file #1: reference.bib
Warning--entry type for "yolo11" isn't style-file defined
--line 291 of file reference.bib
Warning--entry type for "vkdw-x769-21" isn't style-file defined
--line 485 of file reference.bib
Warning--entry type for "yolov5" isn't style-file defined
--line 705 of file reference.bib
Warning--entry type for "yolov8" isn't style-file defined
--line 755 of file reference.bib
-- IEEEtran.bst version 1.14 (2015/08/26) by <PERSON>.
-- http://www.michaelshell.org/tex/ieeetran/bibtex/
-- See the "IEEEtran_bst_HOWTO.pdf" manual for usage information.

Done.
You've used 45 entries,
            4087 wiz_defined-function locations,
            1051 strings with 15868 characters,
and the built_in function-call counts, 32543 in all, are:
= -- 2546
> -- 937
< -- 213
+ -- 515
- -- 208
* -- 1556
:= -- 4829
add.period$ -- 92
call.type$ -- 45
change.case$ -- 45
chr.to.int$ -- 489
cite$ -- 45
duplicate$ -- 2402
empty$ -- 2633
format.name$ -- 223
if$ -- 7565
int.to.chr$ -- 0
int.to.str$ -- 45
missing$ -- 446
newline$ -- 170
num.names$ -- 45
pop$ -- 1127
preamble$ -- 1
purify$ -- 0
quote$ -- 2
skip$ -- 2491
stack$ -- 0
substring$ -- 1301
swap$ -- 1861
text.length$ -- 50
text.prefix$ -- 0
top$ -- 5
type$ -- 45
warning$ -- 0
while$ -- 118
width$ -- 47
write$ -- 446
(There were 4 warnings)
