This is LuaHBTeX, Version 1.18.0 (TeX Live 2024)  (format=lualatex 2024.9.4)  8 AUG 2025 10:58
 restricted system commands enabled.
**yolo_cea_paper.tex
(./yolo_cea_paper.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
Lua module: luaotfload 2024-02-14 3.28 Lua based OpenType font support
Lua module: lualibs 2023-07-13 2.76 ConTeXt Lua standard libraries.
Lua module: lualibs-extended 2023-07-13 2.76 ConTeXt Lua libraries -- extended c
ollection.
luaotfload | conf : Root cache directory is "C:/Users/<USER>/.texlive2024/texmf
-var/luatex-cache/generic/names".
luaotfload | init : Loading fontloader "fontloader-2023-12-28.lua" from kpse-res
olved path "d:/texlive/2024/texmf-dist/tex/luatex/luaotfload/fontloader-2023-12-
28.lua".
Lua-only attribute luaotfload@noligature = 1
luaotfload | init : Context OpenType loader version 3.134
Inserting `luaotfload.node_processor' in `pre_linebreak_filter'.
Inserting `luaotfload.node_processor' in `hpack_filter'.
Inserting `luaotfload.glyph_stream' in `glyph_stream_provider'.
Inserting `luaotfload.define_font' in `define_font'.
Lua-only attribute luaotfload_color_attribute = 2
luaotfload | conf : Root cache directory is "C:/Users/<USER>/.texlive2024/texmf
-var/luatex-cache/generic/names".
Inserting `luaotfload.harf.strip_prefix' in `find_opentype_file'.
Inserting `luaotfload.harf.strip_prefix' in `find_truetype_file'.
Removing  `luaotfload.glyph_stream' from `glyph_stream_provider'.
Inserting `luaotfload.harf.glyphstream' in `glyph_stream_provider'.
Inserting `luaotfload.harf.finalize_vlist' in `post_linebreak_filter'.
Inserting `luaotfload.harf.finalize_hlist' in `hpack_filter'.
Inserting `luaotfload.cleanup_files' in `wrapup_run'.
Inserting `luaotfload.harf.finalize_unicode' in `finish_pdffile'.
Inserting `luaotfload.glyphinfo' in `glyph_info'.
Lua-only attribute luaotfload.letterspace_done = 3
Inserting `luaotfload.aux.set_sscale_dimens' in `luaotfload.patch_font'.
Inserting `luaotfload.aux.set_font_index' in `luaotfload.patch_font'.
Inserting `luaotfload.aux.patch_cambria_domh' in `luaotfload.patch_font'.
Inserting `luaotfload.aux.fixup_fontdata' in `luaotfload.patch_font_unsafe'.
Inserting `luaotfload.aux.set_capheight' in `luaotfload.patch_font'.
Inserting `luaotfload.aux.set_xheight' in `luaotfload.patch_font'.
Inserting `luaotfload.rewrite_fontname' in `luaotfload.patch_font'.
Inserting `tracingstacklevels' in `input_level_string'. (./IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by Michael Shell
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen139
\@IEEEtrantmpdimenB=\dimen140
\@IEEEtrantmpdimenC=\dimen141
\@IEEEtrantmpcountA=\count186
\@IEEEtrantmpcountB=\count187
\@IEEEtrantmpcountC=\count188
\@IEEEtrantmptoksA=\toks17
LaTeX Font Info:    Trying to load font information for TU+ptm on input line 50
3.
LaTeX Font Info:    No file TUptm.fd. on input line 503.

LaTeX Font Warning: Font shape `TU/ptm/m/n' undefined
(Font)              using `TU/lmr/m/n' instead on input line 503.

luaotfload | db : Font names database loaded from C:/Users/<USER>/.texlive2024/
texmf-var/luatex-cache/generic/names/luaotfload-names.luc.gz
-- Using 8.5in x 11in (letter) paper.
-- Using DVI output.
\@IEEEnormalsizeunitybaselineskip=\dimen142
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen143
\CLASSINFOnormalsizeunitybaselineskip=\dimen144
\IEEEnormaljot=\dimen145

LaTeX Font Warning: Font shape `TU/ptm/bx/n' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1086.


LaTeX Font Warning: Font shape `TU/ptm/m/it' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1086.


LaTeX Font Warning: Font shape `TU/ptm/bx/it' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 1086.

\IEEEquantizedlength=\dimen146
\IEEEquantizedlengthdiff=\dimen147
\IEEEquantizedtextheightdiff=\dimen148
\IEEEilabelindentA=\dimen149
\IEEEilabelindentB=\dimen150
\IEEEilabelindent=\dimen151
\IEEEelabelindent=\dimen152
\IEEEdlabelindent=\dimen153
\IEEElabelindent=\dimen154
\IEEEiednormlabelsep=\dimen155
\IEEEiedmathlabelsep=\dimen156
\IEEEiedtopsep=\skip48
\c@section=\count189
\c@subsection=\count190
\c@subsubsection=\count191
\c@paragraph=\count192
\c@IEEEsubequation=\count193
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\c@figure=\count194
\c@table=\count195
\@IEEEeqnnumcols=\count196
\@IEEEeqncolcnt=\count197
\@IEEEsubeqnnumrollback=\count198
\@IEEEquantizeheightA=\dimen157
\@IEEEquantizeheightB=\dimen158
\@IEEEquantizeheightC=\dimen159
\@IEEEquantizeprevdepth=\dimen160
\@IEEEquantizemultiple=\count199
\@IEEEquantizeboxA=\box51
\@IEEEtmpitemindent=\dimen161
\IEEEPARstartletwidth=\dimen162
\c@IEEEbiography=\count266
\@IEEEtranrubishbin=\box52
) (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(d:/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks18
\ex@=\dimen163
))
(d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen164
)
(d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count267
LaTeX Info: Redefining \frac on input line 236.
\uproot@=\count268
\leftroot@=\count269
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count270
\DOTSCASE@=\count271
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen165
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count272
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count273
\dotsspace@=\muskip16
\c@parentequation=\count274
\dspbrk@lvl=\count275
\tag@help=\toks19
\row@=\count276
\column@=\count277
\maxfields@=\count278
\andhelp@=\toks20
\eqnshift@=\dimen166
\alignsep@=\dimen167
\tagshift@=\dimen168
\tagwidth@=\dimen169
\totwidth@=\dimen170
\lineht@=\dimen171
\@envbody=\toks21
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks22
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(d:/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)
(d:/texlive/2024/texmf-dist/tex/latex/newtx/newtxmath.sty
Package: newtxmath 2024/03/06 v1.742

`newtxmath' v1.742, 2024/03/06 Math macros based originally on txfonts (msharpe
) (d:/texlive/2024/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
)
(d:/texlive/2024/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count279
)
(d:/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
)
(d:/texlive/2024/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)

(d:/texlive/2024/texmf-dist/tex/generic/xkeyval/xkeyval.tex
(d:/texlive/2024/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks23
\XKV@tempa@toks=\toks24

(d:/texlive/2024/texmf-dist/tex/generic/xkeyval/keyval.tex))
\XKV@depth=\count280
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))
(d:/texlive/2024/texmf-dist/tex/latex/oberdiek/centernot.sty
Package: centernot 2016/05/16 v1.4 Centers the not symbol horizontally (HO)
)
\tx@cntz=\count281

(d:/texlive/2024/texmf-dist/tex/generic/kastrup/binhex.tex)
\tx@Isdigit=\count282
\tx@IsAlNum=\count283
\tx@tA=\toks25
\tx@tB=\toks26
\tx@su=\read2

amsthm NOT loaded
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 393.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/minntx/m/n on input line 393.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/minntx/m/n on input line 393.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/minntx/m/n --> OT1/minntx/b/n on input line 394.
LaTeX Font Info:    Redeclaring math alphabet \mathsf on input line 401.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> OT1/phv/m/n on input line 401.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> OT1/phv/m/n on input line 401.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/phv/m/n --> OT1/phv/b/n on input line 403.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/minntx/m/it on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/minntx/m/it on input line 410.
LaTeX Font Info:    Redeclaring math alphabet \mathtt on input line 411.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/pcr/m/n on input line 411.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/pcr/m/n on input line 411.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 413.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/minntx/b/n on input line 413.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/minntx/b/n on input line 413.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/minntx/m/it --> OT1/minntx/b/it on input line 414.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  TU/pcr/m/n --> TU/pcr/b/n on input line 417.
LaTeX Font Info:    Redeclaring symbol font `letters' on input line 519.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/ntxmi/m/it on input line 519.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/ntxmi/m/it on input line 519.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/ntxmi/m/it --> OML/ntxmi/b/it on input line 520.
\symlettersA=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `lettersA' in version `bold'
(Font)                  U/ntxmia/m/it --> U/ntxmia/b/it on input line 565.
LaTeX Font Info:    Redeclaring math alphabet \mathfrak on input line 567.
LaTeX Font Info:    Redeclaring symbol font `symbols' on input line 587.
LaTeX Font Info:    Encoding `OMS' has changed to `LMS' for symbol font
(Font)              `symbols' in the math version `normal' on input line 587.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> LMS/ntxsy/m/n on input line 587.
LaTeX Font Info:    Encoding `OMS' has changed to `LMS' for symbol font
(Font)              `symbols' in the math version `bold' on input line 587.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> LMS/ntxsy/m/n on input line 587.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  LMS/ntxsy/m/n --> LMS/ntxsy/b/n on input line 588.
\symAMSm=\mathgroup7
LaTeX Font Info:    Overwriting symbol font `AMSm' in version `bold'
(Font)                  U/ntxsym/m/n --> U/ntxsym/b/n on input line 613.
\symsymbolsC=\mathgroup8
LaTeX Font Info:    Overwriting symbol font `symbolsC' in version `bold'
(Font)                  U/ntxsyc/m/n --> U/ntxsyc/b/n on input line 634.
LaTeX Font Info:    Redeclaring symbol font `largesymbols' on input line 647.
LaTeX Font Info:    Encoding `OMX' has changed to `LMX' for symbol font
(Font)              `largesymbols' in the math version `normal' on input line 6
47.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> LMX/ntxexx/m/n on input line 647.
LaTeX Font Info:    Encoding `OMX' has changed to `LMX' for symbol font
(Font)              `largesymbols' in the math version `bold' on input line 647
.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> LMX/ntxexx/m/n on input line 647.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  LMX/ntxexx/m/n --> LMX/ntxexx/b/n on input line 648.
\symlargesymbolsTXA=\mathgroup9
LaTeX Font Info:    Overwriting symbol font `largesymbolsTXA' in version `bold'

(Font)                  U/ntxexa/m/n --> U/ntxexa/b/n on input line 662.
\tx@sbptoks=\toks27
LaTeX Font Info:    Redeclaring math delimiter \lfloor on input line 885.
LaTeX Font Info:    Redeclaring math delimiter \rfloor on input line 886.
LaTeX Font Info:    Redeclaring math delimiter \lceil on input line 887.
LaTeX Font Info:    Redeclaring math delimiter \rceil on input line 888.
LaTeX Font Info:    Redeclaring math delimiter \lbrace on input line 893.
LaTeX Font Info:    Redeclaring math delimiter \rbrace on input line 894.
LaTeX Font Info:    Redeclaring math delimiter \langle on input line 896.
LaTeX Font Info:    Redeclaring math delimiter \rangle on input line 898.
LaTeX Font Info:    Redeclaring math delimiter \arrowvert on input line 902.
LaTeX Font Info:    Redeclaring math delimiter \vert on input line 903.
LaTeX Font Info:    Redeclaring math accent \dot on input line 974.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 975.
LaTeX Font Info:    Redeclaring math accent \vec on input line 2048.
LaTeX Info: Redefining \Bbbk on input line 2838.
LaTeX Info: Redefining \not on input line 2986.
) (d:/texlive/2024/texmf-dist/tex/latex/algorithms/algorithmic.sty
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'
\c@ALC@unique=\count284
\c@ALC@line=\count285
\c@ALC@rem=\count286
\c@ALC@depth=\count287
\ALC@tlm=\skip54
\algorithmicindent=\skip55
)
(d:/texlive/2024/texmf-dist/tex/latex/algorithms/algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating enviro
nment

(d:/texlive/2024/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count288
\float@exts=\toks28
\float@box=\box55
\@float@everytoks=\toks29
\@floatcapt=\box56
)
\@float@every@algorithm=\toks30
\c@algorithm=\count289
)
(d:/texlive/2024/texmf-dist/tex/latex/tools/array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen172
\ar@mcellbox=\box57
\extrarowheight=\dimen173
\NC@list=\toks31
\extratabsurround=\skip56
\backup@length=\skip57
\ar@cellbox=\box58
)
(d:/texlive/2024/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen174
\lightrulewidth=\dimen175
\cmidrulewidth=\dimen176
\belowrulesep=\dimen177
\belowbottomsep=\dimen178
\aboverulesep=\dimen179
\abovetopsep=\dimen180
\cmidrulesep=\dimen181
\cmidrulekern=\dimen182
\defaultaddspace=\dimen183
\@cmidla=\count290
\@cmidlb=\count291
\@aboverulesep=\dimen184
\@belowrulesep=\dimen185
\@thisruleclass=\count292
\@lastruleclass=\count293
\@thisrulewidth=\dimen186
)
(d:/texlive/2024/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2021/03/15 v2.8 Span multiple rows of a table
\multirow@colwidth=\skip58
\multirow@cntb=\count294
\multirow@dima=\skip59
\bigstrutjot=\dimen187
)
(d:/texlive/2024/texmf-dist/tex/latex/subfig/subfig.sty
Package: subfig 2005/06/28 ver: 1.3 subfig package

(d:/texlive/2024/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen188
\captionmargin=\dimen189
\caption@leftmargin=\dimen190
\caption@rightmargin=\dimen191
\caption@width=\dimen192
\caption@indent=\dimen193
\caption@parindent=\dimen194
\caption@hangindent=\dimen195
Package caption Info: Unknown document class (or package),
(caption)             standard defaults will be used.
Package caption Info: \@makecaption = \long macro:#1#2->\ifx \@captype \@IEEEta
blestring \footnotesize \bgroup \par \centering \@IEEEtabletopskipstrut {\norma
lfont \footnotesize #1}\\{\normalfont \footnotesize \scshape #2}\par \addvspace
 {0.5\baselineskip }\egroup \@IEEEtablecaptionsepspace \else \@IEEEfigurecaptio
nsepspace \setbox \@tempboxa \hbox {\normalfont \footnotesize {#1.}\nobreakspac
e \nobreakspace #2}\ifdim \wd \@tempboxa >\hsize \setbox \@tempboxa \hbox {\nor
malfont \footnotesize {#1.}\nobreakspace \nobreakspace }\parbox [t]{\hsize }{\n
ormalfont \footnotesize \noindent \unhbox \@tempboxa #2}\else \ifCLASSOPTIONcon
ference \hbox to\hsize {\normalfont \footnotesize \hfil \box \@tempboxa \hfil }
\else \hbox to\hsize {\normalfont \footnotesize \box \@tempboxa \hfil }\fi \fi 
\fi  on input line 1175.
)
\c@KVtest=\count295
\sf@farskip=\skip60
\sf@captopadj=\dimen196
\sf@capskip=\skip61
\sf@nearskip=\skip62
\c@subfigure=\count296
\c@subfigure@save=\count297
\c@lofdepth=\count298
\c@subtable=\count299
\c@subtable@save=\count300
\c@lotdepth=\count301
\sf@top=\skip63
\sf@bottom=\skip64
)
(d:/texlive/2024/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2020/02/02 v2.0n Standard LaTeX package
)
(d:/texlive/2024/texmf-dist/tex/latex/sttools/stfloats.sty
Package: stfloats 2017/03/27 v3.3 Improve float mechanism and baselineskip sett
ings
\@dblbotnum=\count302
\c@dblbotnumber=\count303
)
(d:/texlive/2024/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
(d:/texlive/2024/texmf-dist/tex/latex/tools/verbatim.sty
Package: verbatim 2023-11-06 v1.5v LaTeX2e package for verbatim enhancements
\every@verbatim=\toks32
\verbatim@line=\toks33
\verbatim@in@stream=\read3
)
(d:/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(d:/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)

(d:/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
)
(d:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: luatex.def on input line 107.

(d:/texlive/2024/texmf-dist/tex/latex/graphics-def/luatex.def
File: luatex.def 2022/09/22 v1.2d Graphics/color driver for luatex
))
\Gin@req@height=\dimen197
\Gin@req@width=\dimen198
)
(d:/texlive/2024/texmf-dist/tex/latex/cite/cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
)
(d:/texlive/2024/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-01-20 v7.01h Hypertext links for LaTeX

(d:/texlive/2024/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(d:/texlive/2024/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(d:/texlive/2024/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(d:/texlive/2024/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(d:/texlive/2024/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(d:/texlive/2024/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
\pdftexcmds@toks=\toks34
))
(d:/texlive/2024/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(d:/texlive/2024/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
)
(d:/texlive/2024/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(d:/texlive/2024/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(d:/texlive/2024/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(d:/texlive/2024/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count304
)
\@linkdim=\dimen199
\Hy@linkcounter=\count305
\Hy@pagecounter=\count306

(d:/texlive/2024/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-01-20 v7.01h Hyperref: PDFDocEncoding definition (HO)
)
(d:/texlive/2024/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count307

(d:/texlive/2024/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-01-20 v7.01h Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `colorlinks' set `true' on input line 4062.
Package hyperref Info: Hyper figures OFF on input line 4179.
Package hyperref Info: Link nesting OFF on input line 4184.
Package hyperref Info: Hyper index ON on input line 4187.
Package hyperref Info: Plain pages OFF on input line 4194.
Package hyperref Info: Backreferencing OFF on input line 4199.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4446.
\c@Hy@tempcnt=\count308
LaTeX Info: Redefining \url on input line 4784.
\XeTeXLinkMargin=\dimen256

(d:/texlive/2024/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(d:/texlive/2024/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count309
\Field@Width=\dimen257
\Fld@charsize=\dimen258
Package hyperref Info: Hyper figures OFF on input line 6063.
Package hyperref Info: Link nesting OFF on input line 6068.
Package hyperref Info: Hyper index ON on input line 6071.
Package hyperref Info: backreferencing OFF on input line 6078.
Package hyperref Info: Link coloring ON on input line 6081.
Package hyperref Info: Link coloring with OCG OFF on input line 6088.
Package hyperref Info: PDF/A mode OFF on input line 6093.

(d:/texlive/2024/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count310
\c@Item=\count311
\c@Hfootnote=\count312
)
Package hyperref Info: Driver (autodetected): hluatex.

(d:/texlive/2024/texmf-dist/tex/latex/hyperref/hluatex.def
File: hluatex.def 2024-01-20 v7.01h Hyperref driver for luaTeX

(d:/texlive/2024/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
(d:/texlive/2024/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count313
\c@bookmark@seq@number=\count314

(d:/texlive/2024/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(d:/texlive/2024/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip65
)
(d:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-luatex.def
File: l3backend-luatex.def 2024-02-20 L3 backend support: PDF output (LuaTeX)
\l__color_backend_stack_int=\count315
Inserting `l3color' in `luaotfload.parse_color'.
\l__pdf_internal_box=\box59
)

LaTeX Warning: Unused global option(s):
    [lettersize].

(./yolo_cea_paper.aux)
\openout1 = yolo_cea_paper.aux

LaTeX Font Info:    Checking defaults for OML/ntxmi/m/it on input line 30.
LaTeX Font Info:    Trying to load font information for OML+ntxmi on input line
 30.
 (d:/texlive/2024/texmf-dist/tex/latex/newtx/omlntxmi.fd
File: omlntxmi.fd 2015/08/25 Fontinst v1.933 font definitions for OML/ntxmi.
)
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 30.
LaTeX Font Info:    Trying to load font information for TS1+cmr on input line 3
0.
(d:/texlive/2024/texmf-dist/tex/latex/base/ts1cmr.fd
File: ts1cmr.fd 2023/04/13 v2.5m Standard LaTeX font definitions
)
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for U/ntxexa/m/n on input line 30.
LaTeX Font Info:    Trying to load font information for U+ntxexa on input line 
30.

(d:/texlive/2024/texmf-dist/tex/latex/newtx/untxexa.fd
File: untxexa.fd 2012/04/16 Fontinst v1.933 font definitions for U/ntxexa.
)
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for LMS/ntxsy/m/n on input line 30.
LaTeX Font Info:    Trying to load font information for LMS+ntxsy on input line
 30.

(d:/texlive/2024/texmf-dist/tex/latex/newtx/lmsntxsy.fd
File: lmsntxsy.fd 2016/07/02 Fontinst v1.933 font definitions for LMS/ntxsy.
)
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for LMX/ntxexx/m/n on input line 30.
LaTeX Font Info:    Trying to load font information for LMX+ntxexx on input lin
e 30.

(d:/texlive/2024/texmf-dist/tex/latex/newtx/lmxntxexx.fd
File: lmxntxexx.fd 2016/07/03 Fontinst v1.933 font definitions for LMX/ntxexx.
)
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 30.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 30.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 30.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 30.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 30.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 30.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 30.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 30.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 30.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 30.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 30.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 30.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 30.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 30.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 30.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 30.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 30.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 30.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 30.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 30.

-- Lines per column: 58 (exact).
LaTeX Info: Command `\dddot' is already robust on input line 30.
LaTeX Info: Command `\ddddot' is already robust on input line 30.
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: subfig package v1.3 is loaded.
Package caption Info: End \AtBeginDocument code.
(d:/texlive/2024/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count316
\scratchdimen=\dimen259
\scratchbox=\box60
\nofMPsegments=\count317
\nofMParguments=\count318
\everyMPshowfont=\toks35
\MPscratchCnt=\count319
\MPscratchDim=\dimen260
\MPnumerator=\count320
\makeMPintoPDFobject=\count321
\everyMPtoPDFconversion=\toks36
) (d:/texlive/2024/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(d:/texlive/2024/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))
(d:/texlive/2024/texmf-dist/tex/latex/graphics/color.sty
Package: color 2022/01/06 v1.3d Standard LaTeX Color (DPC)

(d:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: luatex.def on input line 149.

(d:/texlive/2024/texmf-dist/tex/latex/graphics/mathcolor.ltx))
Package hyperref Info: Link coloring ON on input line 30.

(./yolo_cea_paper.out) (./yolo_cea_paper.out)
\@outlinefile=\write3

\openout3 = yolo_cea_paper.out


LaTeX Warning: No \author given.


LaTeX Warning: No \author given.


LaTeX Warning: No \author given.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 49.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 57.


LaTeX Font Warning: Font shape `TU/ptm/m/sc' undefined
(Font)              using `TU/ptm/m/n' instead on input line 61.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 62.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 76.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 77.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 78.

[1
Non-PDF special ignored!{d:/texlive/2024/texmf-var/fonts/map/pdftex/updmap/pdfte
x.map}


]

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 80.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 85.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 85.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 86.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 86.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 86.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 87.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 87.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 87.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 91.


LaTeX Warning: Reference `sec:results' on page 2 undefined on input line 91.


warning  (file figures/overfig.pdf) (pdf inclusion): PDF inclusion: found PDF ve
rsion '1.7', but at most version '1.5' allowed
<figures/overfig.pdf, id=113, 575.2692pt x 359.42279pt>
File: figures/overfig.pdf Graphic file (type pdf)
<use figures/overfig.pdf>
Package luatex.def Info: figures/overfig.pdf  used on input line 100.
(luatex.def)             Requested size: 516.0pt x 322.3971pt.

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 105.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 107.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 109.

[2]

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 114.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 116.

LaTeX Font Info:    Trying to load font information for OT1+minntx on input lin
e 116.
(d:/texlive/2024/texmf-dist/tex/latex/newtx/ot1minntx.fd
File: ot1minntx.fd 2023/09/09 v1.1 font definition file for OT1/minntx
)
LaTeX Font Info:    Trying to load font information for U+msa on input line 116
.

(d:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 116
.

(d:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Trying to load font information for U+ntxmia on input line 
116.

(d:/texlive/2024/texmf-dist/tex/latex/newtx/untxmia.fd
File: untxmia.fd 2018/04/14 Fontinst v1.933 font definitions for U/ntxmia.
)
LaTeX Font Info:    Trying to load font information for U+ntxsym on input line 
116.

(d:/texlive/2024/texmf-dist/tex/latex/newtx/untxsym.fd
File: untxsym.fd 2023/08/16 Fontinst v1.933 font definitions for U/ntxsym.
)
LaTeX Font Info:    Trying to load font information for U+ntxsyc on input line 
116.

(d:/texlive/2024/texmf-dist/tex/latex/newtx/untxsyc.fd
File: untxsyc.fd 2012/04/12 Fontinst v1.933 font definitions for U/ntxsyc.
)

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 116.


Underfull \hbox (badness 2951) in paragraph at lines 116--117
[]\TU/ptm/m/sc/10 C3k2-HTC re-places the C3k in C3k2 with the
 []


Underfull \hbox (badness 3098) in paragraph at lines 116--117
\TU/ptm/m/sc/10 HTCBlock (Hy-brid Trans-former Conv Block). The
 []

[3<./figures/overfig.pdf>]

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 127.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 132.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 132.


warning  (file figures/HTC.pdf) (pdf inclusion): PDF inclusion: found PDF versio
n '1.7', but at most version '1.5' allowed
<figures/HTC.pdf, id=162, 421.1233pt x 260.32156pt>
File: figures/HTC.pdf Graphic file (type pdf)
<use figures/HTC.pdf>
Package luatex.def Info: figures/HTC.pdf  used on input line 140.
(luatex.def)             Requested size: 516.0pt x 319.01387pt.

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 147.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 147.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 147.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 147.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 149.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 149.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 149.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 159.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 159.


warning  (file figures/SNI.pdf) (pdf inclusion): PDF inclusion: found PDF versio
n '1.7', but at most version '1.5' allowed
<figures/SNI.pdf, id=165, 477.70468pt x 276.07141pt>
File: figures/SNI.pdf Graphic file (type pdf)
<use figures/SNI.pdf>
Package luatex.def Info: figures/SNI.pdf  used on input line 163.
(luatex.def)             Requested size: 516.0pt x 298.21164pt.
[4] [5<./figures/HTC.pdf><./figures/SNI.pdf>]

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 170.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 172.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 172.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 172.


warning  (file figures/GSConvE.pdf) (pdf inclusion): PDF inclusion: found PDF ve
rsion '1.7', but at most version '1.5' allowed
<figures/GSConvE.pdf, id=237, 343.2825pt x 238.9728pt>
File: figures/GSConvE.pdf Graphic file (type pdf)
<use figures/GSConvE.pdf>
Package luatex.def Info: figures/GSConvE.pdf  used on input line 176.
(luatex.def)             Requested size: 516.0pt x 359.23863pt.

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 185.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 185.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 185.


Underfull \vbox (badness 10000) has occurred while \output is active []



LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 187.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 187.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 189.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 189.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 191.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 191.

[6]
warning  (file figures/SLQMD.pdf) (pdf inclusion): PDF inclusion: found PDF vers
ion '1.7', but at most version '1.5' allowed
<figures/SLQMD.pdf, id=252, 442.5333pt x 284.02109pt>
File: figures/SLQMD.pdf Graphic file (type pdf)
<use figures/SLQMD.pdf>
Package luatex.def Info: figures/SLQMD.pdf  used on input line 205.
(luatex.def)             Requested size: 516.0pt x 331.19386pt.

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 215.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 215.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 215.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 215.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 215.


Underfull \hbox (badness 1509) in paragraph at lines 215--216
[]\TU/ptm/m/sc/10 This study se-lected two widely rec-og-nized pub-lic
 []

[7<./figures/GSConvE.pdf>]

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 226.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 226.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 245.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 245.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 260.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 260.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 260.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 260.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 260.


Underfull \vbox (badness 10000) has occurred while \output is active []

 [8<./figures/SLQMD.pdf>]

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 270.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 274.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 278.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 289.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 297.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 297.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 297.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 297.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 297.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 297.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 299.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 299.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 299.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 299.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 299.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 299.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 304.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 304.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 304.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 304.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 304.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 304.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 304.


warning  (file figures/hotmap.pdf) (pdf inclusion): PDF inclusion: found PDF ver
sion '1.7', but at most version '1.5' allowed
<figures/hotmap.pdf, id=304, 572.1375pt x 463.7325pt>
File: figures/hotmap.pdf Graphic file (type pdf)
<use figures/hotmap.pdf>
Package luatex.def Info: figures/hotmap.pdf  used on input line 311.
(luatex.def)             Requested size: 516.0pt x 418.2328pt.

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 320.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 328.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 328.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 328.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 328.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 328.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 328.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 330.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 330.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 330.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 330.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 330.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 330.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 335.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 335.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 335.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 335.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 335.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 335.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 335.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 342.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 350.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 350.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 350.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 350.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 350.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 350.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 352.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 352.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 352.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 352.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 352.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 352.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 357.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 357.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 357.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 357.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 357.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 357.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 357.

[9] [10<./figures/hotmap.pdf>]

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 372.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 372.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 372.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 372.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 372.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 372.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 372.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 372.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 372.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 372.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 373.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 382.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 382.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 382.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 382.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 382.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 382.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 382.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 387.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 387.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 387.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 387.


warning  (file figures/compared.pdf) (pdf inclusion): PDF inclusion: found PDF v
ersion '1.7', but at most version '1.5' allowed
<figures/compared.pdf, id=366, 536.0025pt x 630.43526pt>
File: figures/compared.pdf Graphic file (type pdf)
<use figures/compared.pdf>
Package luatex.def Info: figures/compared.pdf  used on input line 391.
(luatex.def)             Requested size: 516.0pt x 606.90404pt.

Underfull \vbox (badness 10000) has occurred while \output is active []



LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 400.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 400.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 408.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 408.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 408.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 408.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 408.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 408.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 410.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 410.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 410.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 410.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 410.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 410.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 426.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 427.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 427.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 427.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 427.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 427.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 427.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 427.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 440.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 440.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 440.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 440.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 440.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 440.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 440.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 441.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 454.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 454.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 454.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 454.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 454.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 454.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 454.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 464.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 464.


Underfull \vbox (badness 10000) has occurred while \output is active []

 [11]
[12<./figures/compared.pdf>]

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 466.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 466.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 466.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 466.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 466.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 466.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 468.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 468.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 468.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 468.


Underfull \vbox (badness 10000) has occurred while \output is active []

 [13]
(./yolo_cea_paper.bbl [14]
Underfull \hbox (badness 1824) in paragraph at lines 243--246
[]\TU/ptm/m/sc/8 A. Wang, H. Chen, L. Liu, K. Chen, Z. Lin, J. Han
 []


Underfull \hbox (badness 10000) in paragraph at lines 248--250
\TU/ptm/m/sc/8 centric real-time ob-ject de-tec-tors,'' arXiv preprint
 []

) [15

] (./yolo_cea_paper.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
 ***********


LaTeX Font Warning: Some font shapes were not available, defaults substituted.


LaTeX Warning: There were undefined references.

Package rerunfilecheck Info: File `yolo_cea_paper.out' has not changed.
(rerunfilecheck)             Checksum: A996E55D38AD1C8FF9E2A2BF2CD02587;3205.
)

Here is how much of LuaTeX's memory you used:
 15285 strings out of 476447
 244217,1977958 words of node,token memory allocated
 649 words of node memory still in use:
   6 hlist, 2 vlist, 2 rule, 2 glue, 4 kern, 1 glyph, 33 attribute, 61 glue_spec
, 33 attribute_list, 1 write, 2 pdf_action nodes
   avail lists: 1:1,2:2681,3:902,4:336,5:1219,6:79,7:21415,8:46,9:1140,10:22,11:
2149
 36754 multiletter control sequences out of 65536+600000
 234 fonts using 16385087 bytes
 72i,15n,115p,1755b,495s stack positions out of 10000i,1000n,20000p,200000b,200000s
{d:/texlive/2024/texmf-dist/fonts/enc/dvips/newtx/ntx-ot1-tlf.enc}<d:/texlive/20
24/texmf-dist/fonts/opentype/public/lm/lmroman8-regular.otf><d:/texlive/2024/tex
mf-dist/fonts/opentype/public/lm/lmroman10-regular.otf><d:/texlive/2024/texmf-di
st/fonts/opentype/public/lm/lmroman9-regular.otf><d:/texlive/2024/texmf-dist/fon
ts/opentype/public/lm/lmroman17-regular.otf><d:/texlive/2024/texmf-dist/fonts/op
entype/public/lm/lmroman7-regular.otf><d:/texlive/2024/texmf-dist/fonts/type1/pu
blic/newtx/NewTXMI.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/newtx/NewT
XMI7.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/newtx/txexs.pfb><d:/texl
ive/2024/texmf-dist/fonts/type1/public/newtx/txmiaX.pfb><d:/texlive/2024/texmf-d
ist/fonts/type1/public/newtx/txsym.pfb><d:/texlive/2024/texmf-dist/fonts/type1/p
ublic/newtx/txsys.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/newtx/ztmb.
pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/newtx/ztmr.pfb>
Output written on yolo_cea_paper.pdf (15 pages, 161541414 bytes).

PDF statistics: 540 PDF objects out of 1000 (max. 8388607)
 413 compressed objects within 5 object streams
 110 named destinations out of 1000 (max. 131072)
 137 words of extra memory for PDF output out of 10000 (max. 100000000)

